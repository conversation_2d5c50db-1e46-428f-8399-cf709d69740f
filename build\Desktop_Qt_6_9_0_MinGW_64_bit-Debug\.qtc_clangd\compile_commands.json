[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\AudioApi\\audioread.cpp"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/AudioApi/audioread.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\AudioApi\\audiowrite.cpp"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/AudioApi/audiowrite.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\main.cpp"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\dialog.cpp"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/dialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\AudioApi\\audioread.h"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/AudioApi/audioread.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\AudioApi\\audiowrite.h"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/AudioApi/audiowrite.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\AudioApi\\world.h"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/AudioApi/world.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\dialog.h"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/dialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_MULTIMEDIA_LIB", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\audioapi", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtMultimedia", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\AudioDemo\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\ui_dialog.h"], "directory": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/AudioDemo/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/ui_dialog.h"}]