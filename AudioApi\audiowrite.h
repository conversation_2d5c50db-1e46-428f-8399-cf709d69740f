#ifndef AUDIOWRITE_H
#define AUDIOWRITE_H

#include <QObject>
#include "world.h"


class AudioWrite : public QObject
{
    Q_OBJECT
public:
    explicit AudioWrite(QObject *parent = nullptr);


signals:


public slots:
    void slot_playAudio(QByteArray bt);

private:
    QAudioSink * m_audio_sink; // Qt6使用QAudioSink替代QAudioOutput
    QIODevice* m_buffer_out;
    QAudioFormat format;
    QAudioDevice m_outputDevice; // Qt6音频设备


};

#endif // AUDIOWRITE_H
