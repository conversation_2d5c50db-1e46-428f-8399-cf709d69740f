#ifndef AUDIOREAD_H
#define AUDIOREAD_H

#include <QObject>
#include "world.h"

class AudioRead : public QObject
{
    Q_OBJECT
public:
    explicit AudioRead(QObject *parent = nullptr);

public slots:
    void ReadMore();    //定时超时，从缓冲区读取音频数据
    void start();   //停止
    void pause();   //开始

signals:
    void SIG_audioFrame(QByteArray bt);//音频帧 参数：字节数组

private:
    QAudioSource* m_audio_source;//Qt6音频源
    QIODevice* m_buffer_in;//对应的缓冲区
    QTimer* m_timer;//
    QAudioFormat format;
    QAudioDevice m_inputDevice; // Qt6音频设备
    int m_audioState;//播放状态

};

#endif // AUDIOREAD_H
