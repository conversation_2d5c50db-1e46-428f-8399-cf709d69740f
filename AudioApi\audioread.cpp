#include "audioread.h"


AudioRead::AudioRead(QObject *parent) : QObject{parent}
{
    //声卡采样格式 采样率:8kHZ 位宽:16位 单声道

    format.setSampleRate(8000);
    format.setChannelCount(1);
    format.setSampleSize(16);
    format.setCodec("audio/pcm");
    format.setByteOrder(QAudioFormat::LittleEndian);
    format.setSampleType(QAudioFormat::UnSignedInt);
    QAudioDeviceInfo info = QAudioDeviceInfo::defaultInputDevice();
    if (!info.isFormatSupported(format)) {
        QMessageBox::information(NULL , "提示", "打开音频设备失败");
        format = info.nearestFormat(format);
    }

    //
    m_audio_in = nullptr;
    m_buffer_in = nullptr;
    m_timer = new QTimer;
    connect(m_timer,SIGNAL(timeout()),this,SLOT(ReadMore()));
    m_audioState = stopped;
}

void AudioRead::ReadMore()
{
    if (!m_audio_in)
        return;
    QByteArray m_buffer(2048,0);
    qint64 len = m_audio_in->bytesReady();
    if (len < 640)
    {
        return;
    }
    qint64 l = m_buffer_in->read(m_buffer.data(), 640);
    QByteArray frame;
    frame.append(m_buffer.data(),640);
    Q_EMIT SIG_audioFrame( frame );
}

void AudioRead::start()
{
    if(m_audioState == stopped || m_audioState == pasuing)
    {
        m_audio_in = new QAudioInput(format,this);
        //返回缓冲区地址给成员
        m_buffer_in = m_audio_in->start();//声音采集开始

        m_timer->start(1000/40);

        m_audioState = playing;
    }
}

void AudioRead::pause()
{
    if(m_audioState == playing)
    {
        m_timer->stop();//关闭定时器
        if(m_audio_in)
        {
            m_audio_in->stop();
            delete m_audio_in;
        }
        m_audioState = pasuing;
    }
}
