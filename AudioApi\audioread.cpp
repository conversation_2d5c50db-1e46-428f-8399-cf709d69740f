#include "audioread.h"


AudioRead::AudioRead(QObject *parent) : QObject{parent}
{
    //声卡采样格式 采样率:8kHZ 位宽:16位 单声道
    // Qt6中使用QMediaDevices替代QAudioDeviceInfo
    m_inputDevice = QMediaDevices::defaultAudioInput();

    // 使用设备的首选格式，避免兼容性问题
    format = m_inputDevice.preferredFormat();

    // 如果需要，可以尝试设置特定参数
    if (m_inputDevice.isFormatSupported(format)) {
        // 可以尝试设置为我们需要的格式
        QAudioFormat customFormat;
        customFormat.setSampleRate(8000);
        customFormat.setChannelCount(1);
        customFormat.setSampleFormat(QAudioFormat::Int16);

        if (m_inputDevice.isFormatSupported(customFormat)) {
            format = customFormat;
        }
    }

    //
    m_audio_source = nullptr;
    m_buffer_in = nullptr;
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &AudioRead::ReadMore);
    m_audioState = stopped;

    // 检查音频设备是否可用
    if (m_inputDevice.isNull()) {
        QMessageBox::warning(nullptr, "警告", "未找到可用的音频输入设备");
    }
}

void AudioRead::ReadMore()
{
    if (!m_audio_source || !m_buffer_in)
        return;
    QByteArray m_buffer(2048,0);
    // Qt6中QAudioSource没有bytesReady()方法，直接检查缓冲区可读字节数
    qint64 len = m_buffer_in->bytesAvailable();
    if (len < 640)
    {
        return;
    }
    qint64 l = m_buffer_in->read(m_buffer.data(), 640);
    if (l > 0) {
        QByteArray frame;
        frame.append(m_buffer.data(), l);
        Q_EMIT SIG_audioFrame( frame );

        // 每隔一段时间显示一次调试信息（避免信息过多）
        static int counter = 0;
        counter++;
        if (counter % 100 == 0) { // 每100次显示一次
            qDebug() << "音频数据采集中... 字节数:" << l;
        }
    }
}

void AudioRead::start()
{
    if(m_audioState == stopped || m_audioState == pasuing)
    {
        // 检查设备是否可用
        if (m_inputDevice.isNull()) {
            QMessageBox::warning(nullptr, "错误", "音频输入设备不可用");
            return;
        }

        // Qt6中使用QAudioSource替代QAudioInput
        m_audio_source = new QAudioSource(m_inputDevice, format, this);

        // 检查音频源是否创建成功
        if (!m_audio_source) {
            QMessageBox::warning(nullptr, "错误", "无法创建音频源");
            return;
        }

        //返回缓冲区地址给成员
        m_buffer_in = m_audio_source->start();//声音采集开始

        if (!m_buffer_in) {
            QMessageBox::warning(nullptr, "错误", "无法启动音频采集");
            return;
        }

        m_timer->start(1000/40);
        m_audioState = playing;

        // 显示成功信息
        QMessageBox::information(nullptr, "提示", "音频采集已开始！请对着麦克风说话测试。");
    }
}

void AudioRead::pause()
{
    if(m_audioState == playing)
    {
        m_timer->stop();//关闭定时器
        if(m_audio_source)
        {
            m_audio_source->stop();
            delete m_audio_source;
            m_audio_source = nullptr;
        }
        m_audioState = pasuing;
    }
}
