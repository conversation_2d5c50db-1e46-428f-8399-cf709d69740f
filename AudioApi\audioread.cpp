#include "audioread.h"


AudioRead::AudioRead(QObject *parent) : QObject{parent}
{
    //声卡采样格式 采样率:8kHZ 位宽:16位 单声道

    format.setSampleRate(8000);
    format.setChannelCount(1);
    format.setSampleFormat(QAudioFormat::Int16);

    // Qt6中使用QMediaDevices替代QAudioDeviceInfo
    m_inputDevice = QMediaDevices::defaultAudioInput();
    if (!m_inputDevice.isFormatSupported(format)) {
        QMessageBox::information(nullptr, "提示", "打开音频设备失败");
        // Qt6中没有nearestFormat，需要手动调整格式
        format.setSampleRate(m_inputDevice.preferredFormat().sampleRate());
        format.setChannelCount(m_inputDevice.preferredFormat().channelCount());
    }

    //
    m_audio_source = nullptr;
    m_buffer_in = nullptr;
    m_timer = new QTimer;
    connect(m_timer, &QTimer::timeout, this, &AudioRead::ReadMore);
    m_audioState = stopped;
}

void AudioRead::ReadMore()
{
    if (!m_audio_source || !m_buffer_in)
        return;
    QByteArray m_buffer(2048,0);
    // Qt6中QAudioSource没有bytesReady()方法，直接检查缓冲区可读字节数
    qint64 len = m_buffer_in->bytesAvailable();
    if (len < 640)
    {
        return;
    }
    qint64 l = m_buffer_in->read(m_buffer.data(), 640);
    QByteArray frame;
    frame.append(m_buffer.data(),640);
    Q_EMIT SIG_audioFrame( frame );
}

void AudioRead::start()
{
    if(m_audioState == stopped || m_audioState == pasuing)
    {
        // Qt6中使用QAudioSource替代QAudioInput
        m_audio_source = new QAudioSource(m_inputDevice, format, this);
        //返回缓冲区地址给成员
        m_buffer_in = m_audio_source->start();//声音采集开始

        m_timer->start(1000/40);

        m_audioState = playing;
    }
}

void AudioRead::pause()
{
    if(m_audioState == playing)
    {
        m_timer->stop();//关闭定时器
        if(m_audio_source)
        {
            m_audio_source->stop();
            delete m_audio_source;
            m_audio_source = nullptr;
        }
        m_audioState = pasuing;
    }
}
