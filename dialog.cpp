#include "dialog.h"
#include "ui_dialog.h"

Dialog::<PERSON>alog(QWidget *parent): QDialog(parent) , ui(new Ui::Dialog)
{
    ui->setupUi(this);

    //初始化
    m_pAudioRead = new AudioRead;

    m_pAudioWrite = new AudioWrite;

    //read发信号，write执行槽函数
    connect(m_pAudioRead,SIGNAL(SIG_audioFrame(QByteArray)),m_pAudioWrite,SLOT(slot_playAudio(QByteArray)));
}

Dialog::~Dialog()
{
    delete ui;
}

void Dialog::on_pb_start_clicked()
{
    m_pAudioRead->start();
}


void Dialog::on_pb_pause_clicked()
{
    m_pAudioRead->pause();
}

