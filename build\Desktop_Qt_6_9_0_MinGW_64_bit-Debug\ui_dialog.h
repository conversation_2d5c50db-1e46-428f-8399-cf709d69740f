/********************************************************************************
** Form generated from reading UI file 'dialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_DIALOG_H
#define UI_DIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QPushButton>

QT_BEGIN_NAMESPACE

class Ui_Dialog
{
public:
    QPushButton *pb_start;
    QPushButton *pb_pause;

    void setupUi(QDialog *Dialog)
    {
        if (Dialog->objectName().isEmpty())
            Dialog->setObjectName("Dialog");
        Dialog->resize(800, 600);
        pb_start = new QPushButton(Dialog);
        pb_start->setObjectName("pb_start");
        pb_start->setGeometry(QRect(130, 400, 180, 80));
        pb_pause = new QPushButton(Dialog);
        pb_pause->setObjectName("pb_pause");
        pb_pause->setGeometry(QRect(400, 400, 180, 80));

        retranslateUi(Dialog);

        QMetaObject::connectSlotsByName(Dialog);
    } // setupUi

    void retranslateUi(QDialog *Dialog)
    {
        Dialog->setWindowTitle(QCoreApplication::translate("Dialog", "Dialog", nullptr));
        pb_start->setText(QCoreApplication::translate("Dialog", "\345\274\200\345\247\213", nullptr));
        pb_pause->setText(QCoreApplication::translate("Dialog", "\346\232\202\345\201\234", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Dialog: public Ui_Dialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_DIALOG_H
