#include "audiowrite.h"

AudioWrite::AudioWrite(QObject *parent): QObject{parent}
{
    //声卡采样格式
    format.setSampleRate(8000);
    format.setChannelCount(1);
    format.setSampleSize(16);
    format.setCodec("audio/pcm");
    format.setByteOrder(QAudioFormat::LittleEndian);
    format.setSampleType(QAudioFormat::UnSignedInt);
    QAudioDeviceInfo info = QAudioDeviceInfo::defaultInputDevice();
    if (!info.isFormatSupported(format)) {
        QMessageBox::information(NULL , "提示", "打开音频设备失败");
        format = info.nearestFormat(format);
    }

    m_audio_out = new QAudioOutput(format,this);

    //向buffer里面存数据，就会触发播放声音
    m_buffer_out = m_audio_out->start();



}

void AudioWrite::slot_playAudio(QByteArray bt)
{
    if(bt.size()<640) return ;
    m_buffer_out->write( bt.data() , 640 )
}
