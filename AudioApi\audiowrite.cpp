#include "audiowrite.h"

AudioWrite::AudioWrite(QObject *parent): QObject{parent}
{
    //声卡采样格式
    format.setSampleRate(8000);
    format.setChannelCount(1);
    format.setSampleFormat(QAudioFormat::Int16);

    // Qt6中使用QMediaDevices替代QAudioDeviceInfo
    QAudioDevice outputDevice = QMediaDevices::defaultAudioOutput();
    if (!outputDevice.isFormatSupported(format)) {
        QMessageBox::information(nullptr, "提示", "打开音频设备失败");
        // Qt6中没有nearestFormat，需要手动调整格式
        format.setSampleRate(outputDevice.preferredFormat().sampleRate());
        format.setChannelCount(outputDevice.preferredFormat().channelCount());
    }

    // Qt6中QAudioOutput构造函数需要设备和格式参数
    m_audio_out = new QAudioOutput(outputDevice, format, this);

    //向buffer里面存数据，就会触发播放声音
    m_buffer_out = m_audio_out->start();
}

void AudioWrite::slot_playAudio(QByteArray bt)
{
    if(bt.size()<640) return ;
    m_buffer_out->write( bt.data() , 640 );
}
