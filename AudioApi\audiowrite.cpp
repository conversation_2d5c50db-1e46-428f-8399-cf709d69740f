#include "audiowrite.h"

AudioWrite::AudioWrite(QObject *parent): QObject{parent}
{
    //声卡采样格式
    // Qt6中使用QMediaDevices替代QAudioDeviceInfo
    m_outputDevice = QMediaDevices::defaultAudioOutput();

    // 使用设备的首选格式，避免兼容性问题
    format = m_outputDevice.preferredFormat();

    // 如果需要，可以尝试设置特定参数
    if (m_outputDevice.isFormatSupported(format)) {
        // 可以尝试设置为我们需要的格式
        QAudioFormat customFormat;
        customFormat.setSampleRate(8000);
        customFormat.setChannelCount(1);
        customFormat.setSampleFormat(QAudioFormat::Int16);

        if (m_outputDevice.isFormatSupported(customFormat)) {
            format = customFormat;
        }
    }

    // Qt6中使用QAudioSink替代QAudioOutput
    m_audio_sink = new QAudioSink(m_outputDevice, format, this);

    //向buffer里面存数据，就会触发播放声音
    m_buffer_out = m_audio_sink->start();
}

void AudioWrite::slot_playAudio(QByteArray bt)
{
    if(bt.size() < 640) return ;

    if (m_buffer_out) {
        qint64 written = m_buffer_out->write( bt.data() , bt.size() );

        // 每隔一段时间显示一次调试信息
        static int counter = 0;
        counter++;
        if (counter % 100 == 0) { // 每100次显示一次
            qDebug() << "音频数据播放中... 写入字节数:" << written;
        }
    }
}
