#include "audiowrite.h"

AudioWrite::AudioWrite(QObject *parent): QObject{parent}
{
    //声卡采样格式
    format.setSampleRate(8000);
    format.setChannelCount(1);
    format.setSampleFormat(QAudioFormat::Int16);

    // Qt6中使用QMediaDevices替代QAudioDeviceInfo
    m_outputDevice = QMediaDevices::defaultAudioOutput();
    if (!m_outputDevice.isFormatSupported(format)) {
        QMessageBox::information(nullptr, "提示", "打开音频设备失败");
        // Qt6中没有nearestFormat，需要手动调整格式
        format.setSampleRate(m_outputDevice.preferredFormat().sampleRate());
        format.setChannelCount(m_outputDevice.preferredFormat().channelCount());
    }

    // Qt6中使用QAudioSink替代QAudioOutput
    m_audio_sink = new QAudioSink(m_outputDevice, format, this);

    //向buffer里面存数据，就会触发播放声音
    m_buffer_out = m_audio_sink->start();
}

void AudioWrite::slot_playAudio(QByteArray bt)
{
    if(bt.size()<640) return ;
    m_buffer_out->write( bt.data() , 640 );
}
