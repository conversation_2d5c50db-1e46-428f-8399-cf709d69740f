#############################################################################
# Makefile for building: AudioDemo
# Generated by qmake (3.1) (Qt 6.9.0)
# Project:  AudioDemo.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DISABLE_DEPRECATED_BEFORE=0x060000 -DQT_MULTIMEDIA_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -Iaudioapi -I../../../../PERSONAL/QT/6.9.0/mingw_64/include -I../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia -I../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -I../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui -I../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -I../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore -Idebug -I. -I/include -I../../../../PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Multimedia.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Widgets.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Gui.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Network.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Core.a -lmingw32 D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = AudioApi\audioread.cpp \
		AudioApi\audiowrite.cpp \
		main.cpp \
		dialog.cpp debug\moc_audioread.cpp \
		debug\moc_audiowrite.cpp \
		debug\moc_dialog.cpp
OBJECTS       = debug/audioread.o \
		debug/audiowrite.o \
		debug/main.o \
		debug/dialog.o \
		debug/moc_audioread.o \
		debug/moc_audiowrite.o \
		debug/moc_dialog.o

DIST          =  AudioApi\audioread.h \
		AudioApi\audiowrite.h \
		AudioApi\world.h \
		dialog.h AudioApi\audioread.cpp \
		AudioApi\audiowrite.cpp \
		main.cpp \
		dialog.cpp
QMAKE_TARGET  = AudioDemo
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = AudioDemo.exe
DESTDIR_TARGET = debug\AudioDemo.exe

####### Build rules

first: all
all: Makefile.Debug  debug/AudioDemo.exe

debug/AudioDemo.exe: D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Multimedia.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Widgets.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Gui.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Network.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Core.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6EntryPoint.a ui_dialog.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug AudioDemo.pro

qmake_all: FORCE

dist:
	$(ZIP) AudioDemo.zip $(SOURCES) $(DIST) AudioDemo.pro ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\spec_pre.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\device_config.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\sanitize.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\gcc-base.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\g++-base.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\windows-vulkan.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\g++-win32.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\windows-desktop.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\qconfig.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_freetype.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_libpng.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_core.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_core_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_help.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_help_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_linguist.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_network.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_network_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_png_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_tools_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt_functions.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt_config.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\win32-g++\qmake.conf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exclusive_builds.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\toolchain.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\default_pre.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\default_pre.prf AudioApi\audioapi.pri ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resolve_config.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exclusive_builds_post.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\default_post.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\build_pass.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\precompile_header.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\warn_on.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\permissions.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resources_functions.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resources.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\moc.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\opengl.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\uic.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qmake_use.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\file_copies.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\windows.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\testcase_targets.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exceptions.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\yacc.prf ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\lex.prf AudioDemo.pro ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Multimedia.prl ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Widgets.prl ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Gui.prl ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Network.prl ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Core.prl ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6EntryPoint.prl    ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp AudioApi\audioread.h AudioApi\audiowrite.h AudioApi\world.h dialog.h  AudioApi\audioread.cpp AudioApi\audiowrite.cpp main.cpp dialog.cpp dialog.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\audioread.o debug\audiowrite.o debug\main.o debug\dialog.o debug\moc_audioread.o debug\moc_audiowrite.o debug\moc_dialog.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: ../../../../PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h ..\..\..\..\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_audioread.cpp debug/moc_audiowrite.cpp debug/moc_dialog.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_audioread.cpp debug\moc_audiowrite.cpp debug\moc_dialog.cpp
debug/moc_audioread.cpp: AudioApi/audioread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		AudioApi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h \
		debug/moc_predefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/AudioDemo/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/AudioDemo -ID:/2025/Project/videoconferencing/AudioDemo/audioapi -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include AudioApi\audioread.h -o debug\moc_audioread.cpp

debug/moc_audiowrite.cpp: AudioApi/audiowrite.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		AudioApi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h \
		debug/moc_predefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/AudioDemo/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/AudioDemo -ID:/2025/Project/videoconferencing/AudioDemo/audioapi -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include AudioApi\audiowrite.h -o debug\moc_audiowrite.cpp

debug/moc_dialog.cpp: dialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		audioapi/audioread.h \
		audioapi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h \
		audioapi/audiowrite.h \
		debug/moc_predefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/AudioDemo/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/AudioDemo -ID:/2025/Project/videoconferencing/AudioDemo/audioapi -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include dialog.h -o debug\moc_dialog.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_dialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_dialog.h
ui_dialog.h: dialog.ui \
		../../../../PERSONAL/QT/6.9.0/mingw_64/bin/uic.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\uic.exe dialog.ui -o ui_dialog.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/audioread.o: AudioApi/audioread.cpp AudioApi/audioread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		AudioApi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\audioread.o AudioApi\audioread.cpp

debug/audiowrite.o: AudioApi/audiowrite.cpp AudioApi/audiowrite.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		AudioApi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\audiowrite.o AudioApi\audiowrite.cpp

debug/main.o: main.cpp dialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		audioapi/audioread.h \
		audioapi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h \
		audioapi/audiowrite.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o main.cpp

debug/dialog.o: dialog.cpp dialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		audioapi/audioread.h \
		audioapi/world.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioInput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioinput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qtaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudio.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioOutput \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiooutput.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QIODevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QTimer \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimer.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioFormat \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudioformat.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QMediaDevices \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qmediadevices.h \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/QAudioDevice \
		../../../../PERSONAL/QT/6.9.0/mingw_64/include/QtMultimedia/qaudiodevice.h \
		audioapi/audiowrite.h \
		ui_dialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\dialog.o dialog.cpp

debug/moc_audioread.o: debug/moc_audioread.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_audioread.o debug\moc_audioread.cpp

debug/moc_audiowrite.o: debug/moc_audiowrite.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_audiowrite.o debug\moc_audiowrite.cpp

debug/moc_dialog.o: debug/moc_dialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_dialog.o debug\moc_dialog.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

